<!--packageA/rental/index.wxml-->
<view class="container">
	<view class="top">
		<view class="heard" style="padding-top:{{statusBar}}px;">
			<view class="back" style="height: {{statusBar}}px;top: {{statusBar}}px;" bindtap="getBack"></view>
			<view class="title" style="line-height: {{statusBar}}px;">{{title}}</view>
		</view>
		<view class="location">
			<view>{{equipment.equipment_name}}</view>
			<text>地点：{{equipment.hospital_addr}}</text>
		</view>
	</view>
	<view class="rental" wx:if="{{!upLock}}">
		<image src="/image/icon_lock.png" mode="widthFix" wx:if="{{isUnLock != 3}}"></image>
		<image src="/image/icon_unlock.png" mode="widthFix" wx:else></image>
		<view wx:if="{{isUnLock == 2}}">正在开锁中，请稍后...</view>
		<view wx:if="{{isUnLock == 3}}">
			<text>车位锁已打开</text>
		</view>
	</view>
	<view class="rental" wx:if="{{upLock}}">
		<image src="/image/icon_unlock.png" mode="widthFix" wx:if="{{isLock != 3}}"></image>
		<image src="/image/icon_lock.png" mode="widthFix" wx:else></image>
		<view wx:if="{{isLock == 2}}">正在关锁中，请稍后...</view>
		<view wx:if="{{isLock == 3}}">
			<text>车位锁已关闭</text>
		</view>
		<view wx:if="{{isLock == 1}}">
			<text>车位锁已打开</text>
		</view>
	</view>

	<view class="agreement" wx:if="{{type == 2}}">
		<view class="check" bindtap="getcheck">
			<view class="{{checkbox?'active':''}}">阅读并同意</view>
			<text catchtap="getagreement">《取车协议》</text>
		</view>
	</view>

	<!-- <view class="submit" bindtap="getBlue" wx:if="{{isBlue}}">连接蓝牙</view> -->

	<view class="submit" bindtap="getunLock" wx:if="{{type == 1 && isUnLock != 3}}">立即锁车</view>
	<view class="submit" wx:if="{{type == 1 && isUnLock == 3}}" bindtap="goHome">关锁</view>
	<view class="submit" bindtap="getLock" wx:if="{{type == 2 && !upLock}}">立即取车</view>
	<view class="submit" bindtap="getUpLock" wx:if="{{type == 2 && upLock}}">关锁</view>

	<view class="instruction" wx:if="{{type == 1 && isUnLock != 3}}">
		<view>收费标准：</view>
		<text wx:if="{{equipment.charging_rule == 1}}">{{equipment.price*1 / equipment.hourlong*1}}元/小时</text>
		<text wx:else>{{equipment.contract_price}}元/次</text>
	</view>

	<view class="instruction" wx:if="{{type == 1 && isUnLock == 3}}">
		<view>车辆推入后，再点击</view>
		<text>关锁</text>
	</view>
	
	<!-- <view class="instruction" wx:if="{{type == 2}}">
		<view>车辆取出后，再点击</view>
		<text>关锁按钮</text>
	</view> -->

	<view class="tc" hidden="{{isShow}}">
		<view class="tcxy">
			<view class="close" bindtap="getclose">
				<image src="/image/icon_closeb.png"></image>
			</view>
			<view class="title">取车协议</view>
			<view class="desc">
				<rich-text nodes="{{content}}"></rich-text>
			</view>
		</view>
	</view>

</view>