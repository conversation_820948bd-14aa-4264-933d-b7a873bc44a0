[ 2025-08-07T17:02:41+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-07T17:02:41+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":55920,"STATUS":"81"}',
  'timestamp' => '2025-08-07 17:02:41',
)
error
[ 2025-08-07T17:02:41+08:00 ][ error ] 解析数据：
error
[ 2025-08-07T17:02:41+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 55920,
  'STATUS' => '81',
)
error
[ 2025-08-07T17:02:41+08:00 ][ log ] 【命令响应】收到针对命令包 '55920' 的ACK确认
log
[ 2025-08-07T17:02:41+08:00 ][ log ] 【命令响应】成功更新命令 '55920' 的状态为 'acked'
log
[ 2025-08-07T17:08:24+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-07T17:08:24+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.4","RSSI":22,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-07 17:08:24',
)
error
[ 2025-08-07T17:08:24+08:00 ][ error ] 解析数据：
error
[ 2025-08-07T17:08:24+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.4',
  'RSSI' => 22,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-07T17:08:24+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-07T17:08:24+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-07T17:08:24+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-07T17:08:24+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-07T17:08:24+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-07T17:08:24+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-07T17:08:24+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-07T17:08:24+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":54646,"STATUS":"1"}',
  'timestamp' => '2025-08-07 17:08:24',
)
error
[ 2025-08-07T17:08:24+08:00 ][ error ] 解析数据：
error
[ 2025-08-07T17:08:24+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 54646,
  'STATUS' => '1',
)
error
[ 2025-08-07T17:08:24+08:00 ][ log ] 【命令响应】收到针对命令包 '54646' 的ACK确认
log
[ 2025-08-07T17:08:24+08:00 ][ log ] 【命令响应】成功更新命令 '54646' 的状态为 'acked'
log
